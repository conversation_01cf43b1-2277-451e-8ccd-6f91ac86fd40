# Quick SQL Setup for Supabase

## 🚀 Simple 3-Step Database Setup

### Step 1: Open Supabase SQL Editor
1. Go to your Supabase project dashboard
2. Click on **SQL Editor** in the left sidebar
3. Click **New Query** to create a new SQL script

### Step 2: <PERSON><PERSON> and <PERSON>e the SQL
1. Open the `supabase-schema.sql` file in your project
2. **Select All** (Ctrl+A / Cmd+A) and **Copy** (Ctrl+C / Cmd+C)
3. **Paste** (Ctrl+V / Cmd+V) into the Supabase SQL Editor

### Step 3: Run the SQL
1. Click the **Run** button (or press Ctrl+Enter / Cmd+Enter)
2. Wait for all commands to execute (should take 10-30 seconds)
3. You should see "Success. No rows returned" for most commands

## ✅ What This Creates

The SQL script will automatically create:

### **Tables**
- `User` - User profiles and authentication
- `Task` - User tasks with status and priority
- `TimeBlock` - Time blocks linked to tasks
- `Account` - NextAuth account data
- `Session` - NextAuth session data
- `VerificationToken` - NextAuth verification tokens

### **Security**
- **Row Level Security (RLS)** enabled on all tables
- **Policies** to ensure users can only access their own data
- **Indexes** for fast queries

### **Features**
- **Auto-generated IDs** (CUID-like format)
- **Auto-updating timestamps** (createdAt, updatedAt)
- **Real-time subscriptions** enabled for Task and TimeBlock tables
- **Foreign key constraints** to maintain data integrity

## 🔍 Verify Setup

After running the SQL, check these in your Supabase dashboard:

1. **Database → Tables**: Should show 6 tables
2. **Database → Replication**: Task and TimeBlock should be enabled
3. **Authentication → Policies**: Should show multiple RLS policies

## 🐛 Troubleshooting

### If you get errors:

**"relation already exists"**
- Some tables might already exist
- This is usually safe to ignore

**"permission denied"**
- Make sure you're using the correct Supabase project
- Check that you have admin access

**"syntax error"**
- Make sure you copied the entire SQL file
- Try running smaller sections at a time

### If you need to start over:

```sql
-- Run this to drop all tables and start fresh
DROP TABLE IF EXISTS "TimeBlock" CASCADE;
DROP TABLE IF EXISTS "Task" CASCADE;
DROP TABLE IF EXISTS "Session" CASCADE;
DROP TABLE IF EXISTS "Account" CASCADE;
DROP TABLE IF EXISTS "VerificationToken" CASCADE;
DROP TABLE IF EXISTS "User" CASCADE;
DROP TYPE IF EXISTS "TaskStatus" CASCADE;
DROP TYPE IF EXISTS "Priority" CASCADE;
```

Then run the main SQL script again.

## 🎯 Next Steps

After the database is set up:

1. **Update your `.env.local`** with Supabase credentials
2. **Test the connection** by running your Next.js app
3. **Try creating a task** to verify everything works
4. **Check real-time updates** by opening multiple browser tabs

Your database is now ready for the TimeBlock SaaS application! 🎉
