# Supabase Setup Guide for TimeBlock SaaS

This guide will walk you through setting up Supabase for your TimeBlock SaaS application.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `timeblock-saas`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

## 2. Get Your Project Credentials

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public** key
   - **service_role** key (keep this secret!)

## 3. Update Environment Variables

Update your `.env.local` file with the Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Update DATABASE_URL to point to Supabase
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres"
```

## 4. Set Up Database Schema

### Option A: Using Supabase SQL Editor (Recommended)

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the contents of `supabase-schema.sql` into the editor
3. Click **Run** to execute all the SQL commands
4. This will create:
   - All tables (User, Task, TimeBlock, Account, Session, VerificationToken)
   - Enums (TaskStatus, Priority)
   - Indexes for performance
   - Row Level Security policies
   - Triggers for auto-updating timestamps
   - Functions for generating CUID-like IDs

### Option B: Using Prisma (Alternative)

1. Update your DATABASE_URL in `.env.local` to point to Supabase
2. Run Prisma commands:
   ```bash
   npx prisma db push
   npx prisma generate
   ```

**Note**: Option A is recommended because it includes additional Supabase-specific optimizations like RLS policies and realtime subscriptions.

## 5. Configure Authentication

### Enable Google OAuth in Supabase

1. Go to **Authentication** → **Providers**
2. Enable **Google** provider
3. Add your Google OAuth credentials:
   - **Client ID**: `*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com`
   - **Client Secret**: `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`
4. Add redirect URLs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://your-domain.com/api/auth/callback/google` (production)

### Configure Site URL

1. Go to **Authentication** → **URL Configuration**
2. Set **Site URL**: `http://localhost:3000` (development)
3. Add **Redirect URLs**:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3000/auth/signin`

## 6. Enable Real-time Features

1. Go to **Database** → **Replication**
2. Enable replication for the following tables:
   - `Task`
   - `TimeBlock`
3. This enables real-time subscriptions for live updates

## 7. Verify Database Setup

After running the SQL commands, verify that everything was created correctly:

1. Go to **Database** → **Tables** in your Supabase dashboard
2. You should see all tables: User, Task, TimeBlock, Account, Session, VerificationToken
3. Check **Database** → **Replication** to ensure Task and TimeBlock tables are enabled for realtime
4. Verify **Authentication** → **Policies** shows the RLS policies are active

**Note**: All Row Level Security policies, indexes, and triggers are automatically created by the `supabase-schema.sql` file.

## 8. Test the Connection

1. Start your development server:

   ```bash
   npm run dev
   ```

2. Check the browser console for any connection errors
3. Try creating a task or time block to test the integration

## 9. Optional: Set Up Database Backups

1. Go to **Settings** → **Database**
2. Enable **Point in Time Recovery** for automatic backups
3. Configure backup retention period

## Troubleshooting

### Common Issues:

1. **Connection errors**: Check your environment variables
2. **Authentication issues**: Verify Google OAuth setup
3. **RLS errors**: Ensure policies are correctly set up
4. **Real-time not working**: Check if replication is enabled

### Useful Commands:

```bash
# Reset Prisma client
npx prisma generate

# Push schema changes
npx prisma db push

# View database in Prisma Studio
npx prisma studio
```

## Next Steps

After setup is complete:

1. Test all CRUD operations
2. Verify real-time subscriptions work
3. Test authentication flow
4. Set up monitoring and alerts
5. Configure production environment

Your Supabase integration is now ready! The application will automatically use Supabase for:

- Database operations
- Real-time updates
- Authentication (alongside NextAuth)
- File storage (if needed in the future)
