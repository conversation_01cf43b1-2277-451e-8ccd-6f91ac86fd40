-- Supabase Database Schema for TimeBlock SaaS
-- Run these commands in the Supabase SQL Editor

-- Create custom types (enums)
CREATE TYPE "TaskStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED');
CREATE TYPE "Priority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- Create User table
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" TIMESTAMPTZ,
    "image" TEXT,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- Create Account table (for NextAuth)
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- Create Session table (for NextAuth)
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- Create VerificationToken table (for NextAuth)
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMPTZ NOT NULL
);

-- Create Task table
CREATE TABLE "Task" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" "TaskStatus" NOT NULL DEFAULT 'PENDING',
    "priority" "Priority" NOT NULL DEFAULT 'MEDIUM',
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT "Task_pkey" PRIMARY KEY ("id")
);

-- Create TimeBlock table
CREATE TABLE "TimeBlock" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "startTime" TIMESTAMPTZ NOT NULL,
    "endTime" TIMESTAMPTZ NOT NULL,
    "userId" TEXT NOT NULL,
    "taskId" TEXT,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT "TimeBlock_pkey" PRIMARY KEY ("id")
);

-- Create unique indexes
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- Add foreign key constraints
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Task" ADD CONSTRAINT "Task_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "TimeBlock" ADD CONSTRAINT "TimeBlock_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "TimeBlock" ADD CONSTRAINT "TimeBlock_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create function to generate CUID-like IDs (similar to Prisma's cuid())
CREATE OR REPLACE FUNCTION generate_cuid() RETURNS TEXT AS $$
DECLARE
    timestamp_part TEXT;
    random_part TEXT;
BEGIN
    -- Get current timestamp in base36
    timestamp_part := LPAD(TO_HEX(EXTRACT(EPOCH FROM NOW())::BIGINT), 8, '0');
    
    -- Generate random part
    random_part := LPAD(TO_HEX((RANDOM() * 4294967295)::BIGINT), 8, '0');
    
    -- Combine with 'c' prefix (for cuid)
    RETURN 'c' || timestamp_part || random_part;
END;
$$ LANGUAGE plpgsql;

-- Create function to update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updatedAt
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON "User" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_task_updated_at BEFORE UPDATE ON "Task" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_timeblock_updated_at BEFORE UPDATE ON "TimeBlock" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create triggers to automatically generate IDs (if not provided)
CREATE OR REPLACE FUNCTION set_id_if_empty()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL OR NEW.id = '' THEN
        NEW.id = generate_cuid();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_user_id BEFORE INSERT ON "User" FOR EACH ROW EXECUTE FUNCTION set_id_if_empty();
CREATE TRIGGER set_account_id BEFORE INSERT ON "Account" FOR EACH ROW EXECUTE FUNCTION set_id_if_empty();
CREATE TRIGGER set_session_id BEFORE INSERT ON "Session" FOR EACH ROW EXECUTE FUNCTION set_id_if_empty();
CREATE TRIGGER set_task_id BEFORE INSERT ON "Task" FOR EACH ROW EXECUTE FUNCTION set_id_if_empty();
CREATE TRIGGER set_timeblock_id BEFORE INSERT ON "TimeBlock" FOR EACH ROW EXECUTE FUNCTION set_id_if_empty();

-- Enable Row Level Security (RLS)
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Account" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Session" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Task" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "TimeBlock" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for User table
CREATE POLICY "Users can view own profile" ON "User"
    FOR SELECT USING (auth.uid()::text = id);

CREATE POLICY "Users can update own profile" ON "User"
    FOR UPDATE USING (auth.uid()::text = id);

-- Create RLS policies for Task table
CREATE POLICY "Users can view own tasks" ON "Task"
    FOR SELECT USING (auth.uid()::text = "userId");

CREATE POLICY "Users can insert own tasks" ON "Task"
    FOR INSERT WITH CHECK (auth.uid()::text = "userId");

CREATE POLICY "Users can update own tasks" ON "Task"
    FOR UPDATE USING (auth.uid()::text = "userId");

CREATE POLICY "Users can delete own tasks" ON "Task"
    FOR DELETE USING (auth.uid()::text = "userId");

-- Create RLS policies for TimeBlock table
CREATE POLICY "Users can view own timeblocks" ON "TimeBlock"
    FOR SELECT USING (auth.uid()::text = "userId");

CREATE POLICY "Users can insert own timeblocks" ON "TimeBlock"
    FOR INSERT WITH CHECK (auth.uid()::text = "userId");

CREATE POLICY "Users can update own timeblocks" ON "TimeBlock"
    FOR UPDATE USING (auth.uid()::text = "userId");

CREATE POLICY "Users can delete own timeblocks" ON "TimeBlock"
    FOR DELETE USING (auth.uid()::text = "userId");

-- Create RLS policies for Account and Session tables (NextAuth needs access)
CREATE POLICY "Service role can manage accounts" ON "Account"
    FOR ALL USING (true);

CREATE POLICY "Service role can manage sessions" ON "Session"
    FOR ALL USING (true);

-- Enable realtime for tables that need live updates
ALTER PUBLICATION supabase_realtime ADD TABLE "Task";
ALTER PUBLICATION supabase_realtime ADD TABLE "TimeBlock";

-- Create indexes for better performance
CREATE INDEX "Task_userId_idx" ON "Task"("userId");
CREATE INDEX "Task_status_idx" ON "Task"("status");
CREATE INDEX "Task_priority_idx" ON "Task"("priority");
CREATE INDEX "Task_createdAt_idx" ON "Task"("createdAt");

CREATE INDEX "TimeBlock_userId_idx" ON "TimeBlock"("userId");
CREATE INDEX "TimeBlock_taskId_idx" ON "TimeBlock"("taskId");
CREATE INDEX "TimeBlock_startTime_idx" ON "TimeBlock"("startTime");
CREATE INDEX "TimeBlock_endTime_idx" ON "TimeBlock"("endTime");

-- Insert some sample data (optional)
-- Uncomment the following lines if you want to add sample data

/*
INSERT INTO "User" (id, name, email) VALUES 
('sample-user-1', 'John Doe', '<EMAIL>');

INSERT INTO "Task" (id, title, description, status, priority, "userId") VALUES 
('sample-task-1', 'Sample Task', 'This is a sample task', 'PENDING', 'MEDIUM', 'sample-user-1');

INSERT INTO "TimeBlock" (id, title, "startTime", "endTime", "userId", "taskId") VALUES 
('sample-block-1', 'Work on Sample Task', '2024-01-15 09:00:00+00', '2024-01-15 10:00:00+00', 'sample-user-1', 'sample-task-1');
*/
