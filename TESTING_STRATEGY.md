# Testing Strategy for Supabase Integration

This document outlines the testing approach for the Supabase database integration in the TimeBlock SaaS application.

## Testing Levels

### 1. Unit Tests

Test individual functions and components in isolation.

#### Database Service Tests
- Test CRUD operations for tasks and timeblocks
- Test error handling and edge cases
- Mock Supabase client for isolated testing

#### Hook Tests
- Test custom hooks behavior
- Test real-time subscription setup/cleanup
- Test state management integration

### 2. Integration Tests

Test the interaction between different parts of the system.

#### API Route Tests
- Test all API endpoints with Supabase
- Test authentication and authorization
- Test error responses and status codes

#### Database Integration Tests
- Test actual database operations
- Test data consistency and relationships
- Test transaction handling

### 3. End-to-End Tests

Test complete user workflows.

#### User Journey Tests
- Test complete task creation workflow
- Test time block management
- Test real-time updates across multiple sessions

## Test Setup

### Prerequisites

1. **Test Database**: Set up a separate Supabase project for testing
2. **Environment Variables**: Create `.env.test.local` with test credentials
3. **Test Data**: Create seed data for consistent testing

### Test Environment Configuration

```env
# .env.test.local
NEXT_PUBLIC_SUPABASE_URL=https://your-test-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-test-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-test-service-role-key
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## Test Implementation

### Install Testing Dependencies

```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
```

### Example Test Files

#### 1. Database Service Test

```typescript
// __tests__/lib/supabase-service.test.ts
import { supabaseService } from '@/lib/supabase-service'

describe('SupabaseService', () => {
  beforeEach(async () => {
    // Clean up test data
    await cleanupTestData()
  })

  describe('Task operations', () => {
    it('should create a task', async () => {
      const taskData = {
        title: 'Test Task',
        description: 'Test Description',
        status: 'PENDING' as const,
        priority: 'MEDIUM' as const,
        userId: 'test-user-id'
      }

      const task = await supabaseService.createTask(taskData)
      
      expect(task).toMatchObject(taskData)
      expect(task.id).toBeDefined()
      expect(task.createdAt).toBeDefined()
    })

    it('should handle task creation errors', async () => {
      const invalidTaskData = {
        // Missing required fields
        userId: 'test-user-id'
      }

      await expect(
        supabaseService.createTask(invalidTaskData as any)
      ).rejects.toThrow('Failed to create task')
    })
  })
})
```

#### 2. Hook Test

```typescript
// __tests__/hooks/use-tasks.test.tsx
import { renderHook, act } from '@testing-library/react'
import { useTasks } from '@/hooks/use-tasks'

// Mock the session
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: { user: { id: 'test-user-id' } }
  })
}))

describe('useTasks', () => {
  it('should fetch tasks on mount', async () => {
    const { result } = renderHook(() => useTasks())

    await act(async () => {
      await result.current.refetch()
    })

    expect(result.current.tasks).toBeDefined()
  })

  it('should create a task', async () => {
    const { result } = renderHook(() => useTasks())

    await act(async () => {
      await result.current.createTask({
        title: 'New Task',
        status: 'PENDING',
        priority: 'MEDIUM'
      })
    })

    expect(result.current.tasks).toHaveLength(1)
  })
})
```

#### 3. API Route Test

```typescript
// __tests__/api/tasks.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/tasks/route'

describe('/api/tasks', () => {
  it('should create a task', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        title: 'Test Task',
        description: 'Test Description'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(201)
    const data = JSON.parse(res._getData())
    expect(data.title).toBe('Test Task')
  })

  it('should require authentication', async () => {
    const { req, res } = createMocks({
      method: 'GET'
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(401)
  })
})
```

## Test Data Management

### Seed Data

Create consistent test data for reliable testing:

```typescript
// __tests__/utils/test-data.ts
export const createTestUser = () => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User'
})

export const createTestTask = (userId: string) => ({
  title: 'Test Task',
  description: 'Test Description',
  status: 'PENDING' as const,
  priority: 'MEDIUM' as const,
  userId
})

export const createTestTimeBlock = (userId: string, taskId?: string) => ({
  title: 'Test Time Block',
  startTime: new Date('2024-01-01T09:00:00Z'),
  endTime: new Date('2024-01-01T10:00:00Z'),
  userId,
  taskId
})
```

### Cleanup Utilities

```typescript
// __tests__/utils/cleanup.ts
import { createServerSupabaseClient } from '@/lib/supabase'

export const cleanupTestData = async () => {
  const supabase = createServerSupabaseClient()
  
  // Clean up in reverse dependency order
  await supabase.from('TimeBlock').delete().eq('userId', 'test-user-id')
  await supabase.from('Task').delete().eq('userId', 'test-user-id')
  await supabase.from('User').delete().eq('id', 'test-user-id')
}
```

## Real-time Testing

### Testing Subscriptions

```typescript
// __tests__/real-time/subscriptions.test.ts
import { supabase } from '@/lib/supabase'

describe('Real-time subscriptions', () => {
  it('should receive task updates', (done) => {
    const channel = supabase
      .channel('test-tasks')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'Task'
      }, (payload) => {
        expect(payload.new).toBeDefined()
        done()
      })
      .subscribe()

    // Trigger an insert
    supabaseService.createTask({
      title: 'Real-time Test',
      userId: 'test-user-id'
    })
  })
})
```

## Performance Testing

### Database Query Performance

```typescript
// __tests__/performance/queries.test.ts
describe('Query Performance', () => {
  it('should fetch tasks within acceptable time', async () => {
    const start = Date.now()
    await supabaseService.getTasks('test-user-id')
    const duration = Date.now() - start
    
    expect(duration).toBeLessThan(1000) // Should complete within 1 second
  })
})
```

## Accessibility Testing

Ensure the application remains accessible with Supabase integration:

```typescript
// __tests__/accessibility/components.test.tsx
import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'

expect.extend(toHaveNoViolations)

describe('Accessibility', () => {
  it('should have no accessibility violations', async () => {
    const { container } = render(<TaskList />)
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })
})
```

## Running Tests

### Test Scripts

Add to `package.json`:

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test"
  }
}
```

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts'
  ]
}
```

## Continuous Integration

### GitHub Actions Example

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - run: npm ci
      - run: npm run test:coverage
      
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.TEST_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.TEST_SUPABASE_ANON_KEY }}
```

## Monitoring and Alerts

Set up monitoring for:
- Database connection health
- Query performance
- Error rates
- Real-time subscription status

This comprehensive testing strategy ensures your Supabase integration is robust, performant, and reliable.
