import { supabase, createServerSupabaseClient } from './supabase'
import { Database } from './database.types'

type Tables = Database['public']['Tables']
type Task = Tables['Task']['Row']
type TimeBlock = Tables['TimeBlock']['Row']
type TaskInsert = Tables['Task']['Insert']
type TimeBlockInsert = Tables['TimeBlock']['Insert']
type TaskUpdate = Tables['Task']['Update']
type TimeBlockUpdate = Tables['TimeBlock']['Update']

export class SupabaseService {
  private client = supabase

  // Task operations
  async getTasks(userId: string): Promise<Task[]> {
    try {
      const { data, error } = await this.client
        .from('Task')
        .select('*')
        .eq('userId', userId)
        .order('createdAt', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching tasks:', error)
      throw new Error('Failed to fetch tasks')
    }
  }

  async createTask(task: TaskInsert): Promise<Task> {
    try {
      const { data, error } = await this.client
        .from('Task')
        .insert(task)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating task:', error)
      throw new Error('Failed to create task')
    }
  }

  async updateTask(id: string, updates: TaskUpdate): Promise<Task> {
    try {
      const { data, error } = await this.client
        .from('Task')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating task:', error)
      throw new Error('Failed to update task')
    }
  }

  async deleteTask(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from('Task')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting task:', error)
      throw new Error('Failed to delete task')
    }
  }

  // TimeBlock operations
  async getTimeBlocks(userId: string): Promise<(TimeBlock & { task?: Task })[]> {
    try {
      const { data, error } = await this.client
        .from('TimeBlock')
        .select(`
          *,
          task:Task(*)
        `)
        .eq('userId', userId)
        .order('startTime', { ascending: true })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching time blocks:', error)
      throw new Error('Failed to fetch time blocks')
    }
  }

  async createTimeBlock(timeBlock: TimeBlockInsert): Promise<TimeBlock> {
    try {
      const { data, error } = await this.client
        .from('TimeBlock')
        .insert(timeBlock)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating time block:', error)
      throw new Error('Failed to create time block')
    }
  }

  async updateTimeBlock(id: string, updates: TimeBlockUpdate): Promise<TimeBlock> {
    try {
      const { data, error } = await this.client
        .from('TimeBlock')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating time block:', error)
      throw new Error('Failed to update time block')
    }
  }

  async deleteTimeBlock(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from('TimeBlock')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting time block:', error)
      throw new Error('Failed to delete time block')
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.client
        .from('User')
        .select('id')
        .limit(1)

      return !error
    } catch (error) {
      console.error('Supabase health check failed:', error)
      return false
    }
  }
}

// Server-side service for API routes
export class ServerSupabaseService extends SupabaseService {
  private serverClient = createServerSupabaseClient()

  constructor() {
    super()
    this.client = this.serverClient
  }
}

// Export singleton instances
export const supabaseService = new SupabaseService()
export const serverSupabaseService = new ServerSupabaseService()
