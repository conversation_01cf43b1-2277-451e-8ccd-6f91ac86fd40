import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { serverSupabaseService } from "@/lib/supabase-service"
import { handleApiError, createErrorResponse, AuthenticationError, ValidationError } from "@/lib/error-handler"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      throw new AuthenticationError()
    }

    const tasks = await serverSupabaseService.getTasks(session.user.id)
    return NextResponse.json(tasks)
  } catch (error) {
    const appError = handleApiError(error)
    const errorResponse = createErrorResponse(appError)
    return NextResponse.json(errorResponse, { status: appError.statusCode })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      throw new AuthenticationError()
    }

    const body = await request.json()
    const { title, description, status, priority } = body

    if (!title?.trim()) {
      throw new ValidationError("Title is required")
    }

    const task = await serverSupabaseService.createTask({
      title: title.trim(),
      description: description?.trim() || null,
      status: status || "PENDING",
      priority: priority || "MEDIUM",
      userId: session.user.id,
    })

    return NextResponse.json(task, { status: 201 })
  } catch (error) {
    const appError = handleApiError(error)
    const errorResponse = createErrorResponse(appError)
    return NextResponse.json(errorResponse, { status: appError.statusCode })
  }
}
