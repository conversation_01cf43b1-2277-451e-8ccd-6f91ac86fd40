import { useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useAppStore, Task } from "@/lib/store"
import { supabase } from "@/lib/supabase"

export function useTasks() {
  const { data: session } = useSession()
  const { tasks, setTasks, addTask, updateTask, deleteTask, setIsLoading } = useAppStore()

  const fetchTasks = async () => {
    if (!session) return

    setIsLoading(true)
    try {
      const response = await fetch("/api/tasks")
      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      }
    } catch (error) {
      console.error("Error fetching tasks:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const createTask = async (taskData: Omit<Task, "id" | "userId" | "createdAt" | "updatedAt">) => {
    try {
      const response = await fetch("/api/tasks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      })

      if (response.ok) {
        const newTask = await response.json()
        addTask(newTask)
        return newTask
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to create task")
      }
    } catch (error) {
      console.error("Error creating task:", error)
      throw error
    }
  }

  const updateTaskById = async (id: string, updates: Partial<Task>) => {
    try {
      const response = await fetch(`/api/tasks/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      })

      if (response.ok) {
        const updatedTask = await response.json()
        updateTask(id, updatedTask)
        return updatedTask
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to update task")
      }
    } catch (error) {
      console.error("Error updating task:", error)
      throw error
    }
  }

  const deleteTaskById = async (id: string) => {
    try {
      const response = await fetch(`/api/tasks/${id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        deleteTask(id)
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to delete task")
      }
    } catch (error) {
      console.error("Error deleting task:", error)
      throw error
    }
  }

  // Real-time subscription for tasks
  const subscribeToTasks = useCallback(() => {
    if (!session?.user?.id) return

    const channel = supabase
      .channel('tasks-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Task',
          filter: `userId=eq.${session.user.id}`,
        },
        (payload) => {
          console.log('Task change received:', payload)

          switch (payload.eventType) {
            case 'INSERT':
              if (payload.new) {
                const newTask = {
                  ...payload.new,
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as Task
                addTask(newTask)
              }
              break
            case 'UPDATE':
              if (payload.new) {
                const updatedTask = {
                  ...payload.new,
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as Task
                updateTask(payload.new.id, updatedTask)
              }
              break
            case 'DELETE':
              if (payload.old) {
                deleteTask(payload.old.id)
              }
              break
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [session?.user?.id, addTask, updateTask, deleteTask])

  useEffect(() => {
    if (session) {
      fetchTasks()
      const unsubscribe = subscribeToTasks()
      return unsubscribe
    }
  }, [session, subscribeToTasks])

  return {
    tasks,
    createTask,
    updateTask: updateTaskById,
    deleteTask: deleteTaskById,
    refetch: fetchTasks,
  }
}
