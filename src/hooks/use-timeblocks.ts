import { useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useAppStore, TimeBlock } from "@/lib/store"
import { supabase } from "@/lib/supabase"

export function useTimeBlocks() {
  const { data: session } = useSession()
  const { timeBlocks, setTimeBlocks, addTimeBlock, updateTimeBlock, deleteTimeBlock, setIsLoading } = useAppStore()

  const fetchTimeBlocks = async () => {
    if (!session) return

    setIsLoading(true)
    try {
      const response = await fetch("/api/timeblocks")
      if (response.ok) {
        const data = await response.json()
        setTimeBlocks(data)
      }
    } catch (error) {
      console.error("Error fetching time blocks:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const createTimeBlock = async (timeBlockData: Omit<TimeBlock, "id" | "userId" | "createdAt" | "updatedAt">) => {
    try {
      const response = await fetch("/api/timeblocks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(timeBlockData),
      })

      if (response.ok) {
        const newTimeBlock = await response.json()
        addTimeBlock(newTimeBlock)
        return newTimeBlock
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to create time block")
      }
    } catch (error) {
      console.error("Error creating time block:", error)
      throw error
    }
  }

  const updateTimeBlockById = async (id: string, updates: Partial<TimeBlock>) => {
    try {
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      })

      if (response.ok) {
        const updatedTimeBlock = await response.json()
        updateTimeBlock(id, updatedTimeBlock)
        return updatedTimeBlock
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to update time block")
      }
    } catch (error) {
      console.error("Error updating time block:", error)
      throw error
    }
  }

  const deleteTimeBlockById = async (id: string) => {
    try {
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        deleteTimeBlock(id)
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to delete time block")
      }
    } catch (error) {
      console.error("Error deleting time block:", error)
      throw error
    }
  }

  // Real-time subscription for timeblocks
  const subscribeToTimeBlocks = useCallback(() => {
    if (!session?.user?.id) return

    const channel = supabase
      .channel('timeblocks-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'TimeBlock',
          filter: `userId=eq.${session.user.id}`,
        },
        (payload) => {
          console.log('TimeBlock change received:', payload)

          switch (payload.eventType) {
            case 'INSERT':
              if (payload.new) {
                const newTimeBlock = {
                  ...payload.new,
                  startTime: new Date(payload.new.startTime),
                  endTime: new Date(payload.new.endTime),
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as TimeBlock
                addTimeBlock(newTimeBlock)
              }
              break
            case 'UPDATE':
              if (payload.new) {
                const updatedTimeBlock = {
                  ...payload.new,
                  startTime: new Date(payload.new.startTime),
                  endTime: new Date(payload.new.endTime),
                  createdAt: new Date(payload.new.createdAt),
                  updatedAt: new Date(payload.new.updatedAt),
                } as TimeBlock
                updateTimeBlock(payload.new.id, updatedTimeBlock)
              }
              break
            case 'DELETE':
              if (payload.old) {
                deleteTimeBlock(payload.old.id)
              }
              break
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [session?.user?.id, addTimeBlock, updateTimeBlock, deleteTimeBlock])

  useEffect(() => {
    if (session) {
      fetchTimeBlocks()
      const unsubscribe = subscribeToTimeBlocks()
      return unsubscribe
    }
  }, [session, subscribeToTimeBlocks])

  return {
    timeBlocks,
    createTimeBlock,
    updateTimeBlock: updateTimeBlockById,
    deleteTimeBlock: deleteTimeBlockById,
    refetch: fetchTimeBlocks,
  }
}
